// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

// eslint-disable-next-line import/order
import React from "react";

import "../styles/main.scss";
import { FaFacebook, FaTwitter, FaWhatsapp } from "react-icons/fa";

import img from "../../../images/icon50x50.png";

const Footer = () => {
  return (
    <div className="footer-section">
      <div className="footer-section-content">
        <p className="description">
          استثمر في أمان شركتك من خلال تمكين موظفيك بأفضل تدريب متاح.{" "}
          <span className="text"> اتصل بنا </span>
          لجدولة استشارة أو <span className="text">سجل فريقك</span> في جلسة
          التدريب القادمة.
        </p>
        <div>
          <button className="start-button"> إبداء اليوم</button>
          <div className="divider" />
        </div>
        <div className="footer-bottom">
          <div>
            <div className="footer-bottom-content">
              <img src={img} width={33}
              height={33} alt="Logo" />
              <p>مساحة عمل سوفا</p>
            </div>
            <p className="copy-right">مساحة عمل سوفا . جميع الحقوق محفوظة.</p>
          </div>
          <div className="social-links">
            <FaWhatsapp />
            <FaTwitter />
            <FaFacebook />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Footer;
