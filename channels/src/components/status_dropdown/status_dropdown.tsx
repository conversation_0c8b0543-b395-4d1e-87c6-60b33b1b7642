// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import classNames from 'classnames';
import moment from 'moment-timezone';
import React from 'react';
import type {ReactNode} from 'react';
import {injectIntl, FormattedDate, FormattedMessage, FormattedTime, defineMessage, defineMessages} from 'react-intl';
import type {IntlShape, MessageDescriptor} from 'react-intl';

import StatusIcon from '@mattermost/compass-components/components/status-icon'; // eslint-disable-line no-restricted-imports
import Text from '@mattermost/compass-components/components/text'; // eslint-disable-line no-restricted-imports
import type {TUserStatus} from '@mattermost/compass-components/shared'; // eslint-disable-line no-restricted-imports
import {AccountOutlineIcon, CancelIcon, CheckIcon, ExitToAppIcon} from '@mattermost/compass-icons/components';
import {PulsatingDot} from '@mattermost/components';
import type {PreferenceType} from '@mattermost/types/preferences';
import {CustomStatusDuration} from '@mattermost/types/users';
import type {UserCustomStatus, UserProfile, UserStatus} from '@mattermost/types/users';

import * as GlobalActions from 'actions/global_actions';

import CustomStatusEmoji from 'components/custom_status/custom_status_emoji';
import CustomStatusModal from 'components/custom_status/custom_status_modal';
import CustomStatusText from 'components/custom_status/custom_status_text';
import CustomUserStatusModal from 'components/custom_status/custom_user_status_modal';
import ExpiryTime from 'components/custom_status/expiry_time';
import DndCustomTimePicker from 'components/dnd_custom_time_picker_modal';
import {OnboardingTaskCategory, OnboardingTasksName, TaskNameMapToSteps, CompleteYourProfileTour} from 'components/onboarding_tasks';
import ResetStatusModal from 'components/reset_status_modal';
import UserSettingsModal from 'components/user_settings/modal';
import EmojiIcon from 'components/widgets/icons/emoji_icon';
import Menu from 'components/widgets/menu/menu';
import MenuWrapper from 'components/widgets/menu/menu_wrapper';
import Avatar from 'components/widgets/users/avatar/avatar';
import type {TAvatarSizeToken} from 'components/widgets/users/avatar/avatar';
import WithTooltip from 'components/with_tooltip';

import {ModalIdentifiers, UserStatuses} from 'utils/constants';
import {getBrowserTimezone, getCurrentDateTimeForTimezone, getCurrentMomentForTimezone} from 'utils/timezone';

import type {ModalData} from 'types/actions';
import type {Menu as MenuType} from 'types/store/plugins';

// eslint-disable-next-line import/order
import {isAdmin} from 'mattermost-redux/utils/user_utils';

import './status_dropdown.scss';
import apk1Path from '../../images/app/sofa-tablet-zaid.apk';
import exePath from '../../images/app/SofaChat-2025-15-10.exe';
import apkPath from '../../images/app/SofaChat.apk';

// eslint-disable-next-line import/order
import {Client4} from 'mattermost-redux/client';

// eslint-disable-next-line import/order
import type {PluginManifest} from '@mattermost/types/plugins';

type Props = {
    intl: IntlShape;
    status?: string;
    userId: string;
    profilePicture?: string;
    autoResetPref?: string;
    actions: {
        openModal: <P>(modalData: ModalData<P>) => void;
        setStatus: (status: UserStatus) => void;
        unsetCustomStatus: () => void;
        savePreferences: (userId: string, preferences: PreferenceType[]) => void;
        setStatusDropdown: (open: boolean) => void;
    };
    customStatus?: UserCustomStatus;
    currentUser: UserProfile;
    isCustomStatusEnabled: boolean;
    isCustomStatusExpired: boolean;
    isMilitaryTime: boolean;
    isStatusDropdownOpen: boolean;
    showCompleteYourProfileTour: boolean;
    showCustomStatusPulsatingDot: boolean;
    timezone?: string;
    dndEndTime?: number;
}

type State = {
    openUp: boolean;
    width: number;
    isStatusSet: boolean;
    plugins: PluginManifest[];
};

export const statusDropdownMessages: Record<string, Record<string, MessageDescriptor>> = {
    ooo: defineMessages({
        name: {
            id: 'status_dropdown.set_ooo',
            defaultMessage: 'Out of office',
        },
        extra: {
            id: 'status_dropdown.set_ooo.extra',
            defaultMessage: 'Automatic Replies are enabled',
        },
    }),
    online: defineMessages({
        name: {
            id: 'status_dropdown.set_online',
            defaultMessage: 'Online',
        },
    }),
    away: defineMessages({
        name: {
            id: 'status_dropdown.set_away',
            defaultMessage: 'Away',
        },
    }),
    dnd: defineMessages({
        name: {
            id: 'status_dropdown.set_dnd',
            defaultMessage: 'Do not disturb',
        },
    }),
    offline: defineMessages({
        name: {
            id: 'status_dropdown.set_offline',
            defaultMessage: 'Offline',
        },
    }),
};

export class StatusDropdown extends React.PureComponent<Props, State> {
    dndTimes = [
        {id: 'dont_clear', label: defineMessage({id: 'status_dropdown.dnd_sub_menu_item.dont_clear', defaultMessage: 'Don\'t clear'})},
        {id: 'thirty_minutes', label: defineMessage({id: 'status_dropdown.dnd_sub_menu_item.thirty_minutes', defaultMessage: '30 mins'})},
        {id: 'one_hour', label: defineMessage({id: 'status_dropdown.dnd_sub_menu_item.one_hour', defaultMessage: '1 hour'})},
        {id: 'two_hours', label: defineMessage({id: 'status_dropdown.dnd_sub_menu_item.two_hours', defaultMessage: '2 hours'})},
        {id: 'tomorrow', label: defineMessage({id: 'status_dropdown.dnd_sub_menu_item.tomorrow', defaultMessage: 'Tomorrow'})},
        {id: 'custom', label: defineMessage({id: 'status_dropdown.dnd_sub_menu_item.custom', defaultMessage: 'Choose date and time'})},
    ];
    static defaultProps = {
        userId: '',
        profilePicture: '',
    };

    constructor(props: Props) {
        super(props);

        this.state = {
            openUp: false,
            width: 0,
            isStatusSet: false,
            plugins: [],
        };
    }

    componentDidMount = () => {
        this.getPlugins();
    };

    openProfileModal = (): void => {
        this.props.actions.openModal({
            modalId: ModalIdentifiers.USER_SETTINGS,
            dialogType: UserSettingsModal,
            dialogProps: {isContentProductSettings: false},
        });
    };

    setStatus = (status: string, dndEndTime?: number): void => {
        this.props.actions.setStatus({
            user_id: this.props.userId,
            status,
            dnd_end_time: dndEndTime,
        });
    };

    isUserOutOfOffice = (): boolean => {
        return this.props.status === UserStatuses.OUT_OF_OFFICE;
    };

    setOnline = (event: Event): void => {
        event.preventDefault();
        this.setStatus(UserStatuses.ONLINE);
    };

    setOffline = (event: Event): void => {
        event.preventDefault();
        this.setStatus(UserStatuses.OFFLINE);
    };

    setAway = (event: Event): void => {
        event.preventDefault();
        this.setStatus(UserStatuses.AWAY);
    };
    Downloadmobile = (): void => {
        // eslint-disable-next-line global-require
        // const filePath = require('../../images/app/SofaChat.apk');
        const link = document.createElement('a');
        link.href = `${apkPath}`;
        link.download = 'SofaChat.apk';
        link.click();
    };
    Downloadtablet = (): void => {
        // eslint-disable-next-line global-require
        // const filePath = require('../../images/app/SofaChat.apk');
        const link = document.createElement('a');
        link.href = `${apk1Path}`;
        link.download = 'SofaChat.apk';
        link.click();
    };
    Downloadwindows = (): void => {
        // eslint-disable-next-line global-require
        // const filePath = require('../../images/app/Sofachat_Desktop.exe');
        const link = document.createElement('a');
        link.href = `${exePath}`;
        link.download = 'SofaChat-2025-15-10.exe';
        link.click();
    };

    getPlugins = async () => {
        try {
            const plugins = await Client4.getPlugins();
            this.setState({plugins: plugins.active});
        } catch (error) {
            // eslint-disable-next-line no-console
            console.error('Failed to fetch plugins:', error);
        }
    };

    setDnd = (index: number): void => {
        const currentDate = getCurrentMomentForTimezone(this.props.timezone);
        let endTime = currentDate;
        switch (index) {
        case 0:
            endTime = moment(0);
            break;
        case 1:
            // add 30 minutes in current time
            endTime = currentDate.add(30, 'minutes');
            break;
        case 2:
            // add 1 hour in current time
            endTime = currentDate.add(1, 'hour');
            break;
        case 3:
            // add 2 hours in current time
            endTime = currentDate.add(2, 'hours');
            break;
        case 4:
            // set to next day 9 in the morning
            endTime = currentDate.add(1, 'day').set({hour: 9, minute: 0});
            break;
        }

        this.setStatus(UserStatuses.DND, endTime.utc().unix());
    };

    setCustomTimedDnd = (): void => {
        const dndCustomTimePicker = {
            modalId: ModalIdentifiers.DND_CUSTOM_TIME_PICKER,
            dialogType: DndCustomTimePicker,
            dialogProps: {
                currentDate: this.props.timezone ? getCurrentDateTimeForTimezone(this.props.timezone) : new Date(),
            },
        };

        this.props.actions.openModal(dndCustomTimePicker);
    };

    showStatusChangeConfirmation = (status: string): void => {
        const resetStatusModalData = {
            modalId: ModalIdentifiers.RESET_STATUS,
            dialogType: ResetStatusModal,
            dialogProps: {newStatus: status},
        };

        this.props.actions.openModal(resetStatusModalData);
    };

    renderProfilePicture = (size: TAvatarSizeToken): ReactNode => {
        if (!this.props.profilePicture) {
            return null;
        }
        return (
            <Avatar
                size={size}
                url={this.props.profilePicture}
                tabIndex={undefined}
            />
        );
    };

    handleClearStatus = (e: React.MouseEvent<HTMLButtonElement> | React.MouseEvent<HTMLDivElement> | React.TouchEvent): void => {
        e.stopPropagation();
        e.preventDefault();
        this.props.actions.unsetCustomStatus();
    };

    handleEmitUserLoggedOutEvent = (): void => {
        GlobalActions.emitUserLoggedOutEvent();
    };

    onToggle = (open: boolean): void => {
        this.props.actions.setStatusDropdown(open);
    };

    handleCompleteYourProfileTask = (): void => {
        const taskName = OnboardingTasksName.COMPLETE_YOUR_PROFILE;
        const steps = TaskNameMapToSteps[taskName];
        const currentUserId = this.props.currentUser.id;
        const preferences = [
            {
                user_id: currentUserId,
                category: OnboardingTaskCategory,
                name: taskName,
                value: steps.FINISHED.toString(),
            },
        ];
        this.props.actions.savePreferences(currentUserId, preferences);
    };

    handleCustomStatusEmojiClick = (event: React.MouseEvent): void => {
        event.stopPropagation();
        const customStatusInputModalData = {
            modalId: ModalIdentifiers.CUSTOM_STATUS,
            dialogType: CustomStatusModal,
        };
        this.props.actions.openModal(customStatusInputModalData);
    };

    renderCustomStatus = (isStatusSet: boolean | undefined): ReactNode => {
        if (!this.props.isCustomStatusEnabled) {
            return null;
        }
        const {customStatus} = this.props;

        let customStatusText;
        let customStatusHelpText;
        switch (true) {
        case isStatusSet && customStatus?.text && customStatus.text.length > 0:
            customStatusText = customStatus?.text;
            break;
        case isStatusSet && !customStatus?.text && customStatus?.duration === CustomStatusDuration.DONT_CLEAR:
            customStatusHelpText = this.props.intl.formatMessage({id: 'status_dropdown.set_custom_text', defaultMessage: 'Set custom status text...'});
            break;
        case isStatusSet && !customStatus?.text && customStatus?.duration !== CustomStatusDuration.DONT_CLEAR:
            customStatusText = '';
            break;
        case !isStatusSet:
            customStatusHelpText = this.props.intl.formatMessage({id: 'status_dropdown.set_custom', defaultMessage: 'Set a custom status'});
        }

        const customStatusEmoji = isStatusSet ? (
            <span className='d-flex'>
                <CustomStatusEmoji
                    showTooltip={false}
                    emojiStyle={{marginLeft: 0}}
                />
            </span>
        ) : (
            <EmojiIcon className={'custom-status-emoji'}/>
        );
        const customUserStatus = (
            <svg
                xmlns='http://www.w3.org/2000/svg'
                viewBox='0 0 24 24'
                width='20'
                height='20'
                fill='none'
                stroke='currentColor'
                strokeWidth='2'
                strokeLinecap='round'
                strokeLinejoin='round'
            >

                <circle
                    cx='12'
                    cy='12'
                    r='10'
                    strokeDasharray='3 3'
                />

                <circle
                    cx='12'
                    cy='12'
                    r='6'
                />
            </svg>
        );

        const pulsatingDot = !isStatusSet && this.props.showCustomStatusPulsatingDot && (
            <PulsatingDot/>
        );

        const clearableTooltipText = (
            <FormattedMessage
                id={'input.clear'}
                defaultMessage='Clear'
            />
        );

        const clearButton = isStatusSet && !pulsatingDot && (
            <div
                className={classNames('status-dropdown-menu__clear-container', 'input-clear visible')}
                onClick={this.handleClearStatus}
                onTouchEnd={this.handleClearStatus}
            >
                <WithTooltip
                    title={clearableTooltipText}
                >
                    <span
                        className='input-clear-x'
                        aria-hidden='true'
                    >
                        <i className='icon icon-close-circle'/>
                    </span>
                </WithTooltip>
            </div>
        );

        const expiryTime = isStatusSet && customStatus?.expires_at && customStatus.duration !== CustomStatusDuration.DONT_CLEAR &&
            (
                <ExpiryTime
                    time={customStatus.expires_at}
                    timezone={this.props.timezone}
                    className={classNames('custom_status__expiry', {
                        padded: customStatus?.text?.length > 0,
                    })}
                    withinBrackets={true}
                />
            );

        return (
            <Menu.Group>
                <Menu.ItemToggleModalRedux
                    ariaLabel={customStatusText || customStatusHelpText}
                    modalId={ModalIdentifiers.CUSTOM_STATUS}
                    dialogType={CustomStatusModal}
                    className={classNames('MenuItem__primary-text custom_status__row', {
                        flex: customStatus?.text?.length === 0,
                    })}
                    id={'status-menu-custom-status'}
                >
                    <span className='custom_status__container'>
                        <span className='custom_status__icon'>
                            {customStatusEmoji}
                        </span>
                        <CustomStatusText
                            text={customStatusText}
                            className='custom_status__text'
                        />
                        <Text
                            margin='none'
                        >
                            {customStatusHelpText}
                        </Text>
                        {clearButton}
                        {pulsatingDot}
                    </span>
                    {expiryTime}
                </Menu.ItemToggleModalRedux>

                {this.state.plugins &&
                this.state.plugins.length > 0 &&
                this.state.plugins.some((plugin: PluginManifest) => plugin.id === 'com.example.status') && (
                    <Menu.ItemToggleModalRedux
                        modalId={ModalIdentifiers.CUSTOM_USER_STATUS}
                        dialogType={CustomUserStatusModal}
                        className={classNames('MenuItem__primary-text custom_status__row', {
                            flex: customStatus?.text?.length === 0,
                        })}
                        id={'user-status-menu-custom-statuss'}
                    >
                        <div className='custom_status__container'>
                            <span className='custom_status__icon'>
                                {customUserStatus}
                            </span>
                            <div> {'نشر قصة '}</div>
                        </div>
                    </Menu.ItemToggleModalRedux>
                )}
            </Menu.Group>
        );
    };

    renderDndExtraText = (dndEndTime?: number, timezone?: string) => {
        if (!(dndEndTime && dndEndTime > 0)) {
            return this.props.intl.formatMessage({id: 'status_dropdown.set_dnd.extra', defaultMessage: 'Disables all notifications'});
        }

        const tz = timezone || getBrowserTimezone();
        const currentTime = moment().tz(tz);
        const endTime = moment.unix(dndEndTime).tz(tz);

        let formattedEndTime;

        const diffDays = endTime.clone().startOf('day').diff(currentTime.clone().startOf('day'), 'days');

        switch (diffDays) {
        case 0:
            formattedEndTime = (
                <FormattedMessage
                    id='custom_status.expiry.until'
                    defaultMessage='Until {time}'
                    values={{time: endTime.format('h:mm A')}}
                />
            );
            break;
        case 1:
            formattedEndTime = (
                <FormattedMessage
                    id='custom_status.expiry.until_tomorrow'
                    defaultMessage='Until Tomorrow {time}'
                    values={{time: endTime.format('h:mm A')}}
                />
            );
            break;
        default:
            formattedEndTime = (
                <FormattedMessage
                    id='custom_status.expiry.until'
                    defaultMessage='Until {time}'
                    values={{time: endTime.format('lll')}}
                />
            );
        }

        return formattedEndTime;
    };

    render = (): JSX.Element => {
        const {intl} = this.props;
        const needsConfirm = this.isUserOutOfOffice() && this.props.autoResetPref === '';
        const {status, customStatus, isCustomStatusExpired, currentUser, timezone, dndEndTime} = this.props;
        const isStatusSet = customStatus && !isCustomStatusExpired && (customStatus.text?.length > 0 || customStatus.emoji?.length > 0);

        const setOnline = needsConfirm ? () => this.showStatusChangeConfirmation('online') : this.setOnline;
        const setDnd = needsConfirm ? () => this.showStatusChangeConfirmation('dnd') : this.setDnd;
        const setAway = needsConfirm ? () => this.showStatusChangeConfirmation('away') : this.setAway;
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const setOffline = needsConfirm ? () => this.showStatusChangeConfirmation('offline') : this.setOffline;
        const setCustomTimedDnd = needsConfirm ? () => this.showStatusChangeConfirmation('dnd') : this.setCustomTimedDnd;

        const isCurrentUserAdmin = isAdmin(currentUser.roles);

        const selectedIndicator = (
            <CheckIcon
                size={16}
                color={'var(--button-bg)'}
            />
        );

        const dndSubMenuItems = ([
            {
                id: 'dndSubMenu-header',
                direction: 'right',
                text: this.props.intl.formatMessage({id: 'status_dropdown.dnd_sub_menu_header', defaultMessage: 'Clear after:'}),
                isHeader: true,
            },
        ] as MenuType[])?.concat(
            this.dndTimes.map<MenuType>(({id, label}, index) => {
                let text: MenuType['text'] = this.props.intl.formatMessage(label);
                if (index === 4) {
                    const tomorrow = getCurrentMomentForTimezone(this.props.timezone).add(1, 'day').set({hour: 9, minute: 0}).toDate();
                    text = (
                        <>
                            {text}
                            <span className={`dndTime-${id}_timestamp`}>
                                <FormattedDate
                                    value={tomorrow}
                                    weekday='short'
                                    timeZone={this.props.timezone}
                                />
                                {', '}
                                <FormattedTime
                                    value={tomorrow}
                                    timeStyle='short'
                                    hour12={!this.props.isMilitaryTime}
                                    timeZone={this.props.timezone}
                                />
                            </span>
                        </>
                    );
                }
                return {
                    id: `dndTime-${id}`,
                    direction: 'right',
                    text,
                    action:
                        index === 5 ? () => setCustomTimedDnd() : () => setDnd(index),
                };
            }),
        );

        const customStatusComponent = this.renderCustomStatus(isStatusSet);

        let menuAriaLabeltext;
        switch (this.props.status) {
        case UserStatuses.AWAY:
            menuAriaLabeltext = intl.formatMessage({
                id: 'status_dropdown.profile_button_label.away',
                defaultMessage: 'Current status: Away. Select to open profile and status menu.',
            });
            break;
        case UserStatuses.DND:
            menuAriaLabeltext = intl.formatMessage({
                id: 'status_dropdown.profile_button_label.dnd',
                defaultMessage: 'Current status: Do not disturb. Select to open profile and status menu.',
            });
            break;
        case UserStatuses.OFFLINE:
            menuAriaLabeltext = intl.formatMessage({
                id: 'status_dropdown.profile_button_label.offline',
                defaultMessage: 'Current status: Offline. Select to open profile and status menu.',
            });
            break;
        case UserStatuses.ONLINE:
            menuAriaLabeltext = intl.formatMessage({
                id: 'status_dropdown.profile_button_label.online',
                defaultMessage: 'Current status: Online. Select to open profile and status menu.',
            });
            break;
        case UserStatuses.OUT_OF_OFFICE:
            menuAriaLabeltext = intl.formatMessage({
                id: 'status_dropdown.profile_button_label.ooo',
                defaultMessage: 'Current status: Out of office. Select to open profile and status menu.',
            });
            break;
        default:
            menuAriaLabeltext = intl.formatMessage({
                id: 'status_dropdown.profile_button_label',
                defaultMessage: 'Select to open profile and status menu.',
            });
        }

        const dndExtraText = this.renderDndExtraText(dndEndTime, timezone);

        return (
            <MenuWrapper
                onToggle={this.onToggle}
                open={this.props.isStatusDropdownOpen}
                className={classNames('status-dropdown-menu status-dropdown-menu-global-header', {
                    active: this.props.isStatusDropdownOpen || isStatusSet,
                })}
            >
                <button
                    className='status-wrapper style--none'
                    aria-label={menuAriaLabeltext}
                    aria-expanded={this.props.isStatusDropdownOpen}
                    aria-controls='statusDropdownMenu'
                >
                    <CustomStatusEmoji
                        showTooltip={true}
                        emojiStyle={{marginRight: '6px'}}
                        onClick={this.handleCustomStatusEmojiClick as () => void}
                    />
                    {this.renderProfilePicture('sm')}
                    <div
                        className='status'
                    >
                        <StatusIcon
                            size={'sm'}
                            status={(this.props.status || 'offline') as TUserStatus}
                        />
                    </div>
                </button>
                <Menu
                    ariaLabel={this.props.intl.formatMessage({id: 'status_dropdown.menuAriaLabel', defaultMessage: 'Set a status'})}
                    id={'statusDropdownMenu'}
                    listId={'status-drop-down-menu-list'}
                >
                    {currentUser && (
                        <Menu.Header onClick={this.openProfileModal}>
                            {this.renderProfilePicture('lg')}
                            <div className={'username-wrapper'}>
                                <Text
                                    className={'bold'}
                                    margin={'none'}
                                >{`${currentUser.first_name} ${currentUser.last_name}`}</Text>
                                <Text
                                    margin={'none'}
                                    className={!currentUser.first_name && !currentUser.last_name ? 'bold' : 'contrast'}
                                    color={!currentUser.first_name && !currentUser.last_name ? undefined : 'inherit'}
                                >
                                    {'@' + currentUser.username}
                                </Text>
                            </div>
                        </Menu.Header>
                    )}
                    <Menu.Group>
                        <Menu.ItemAction
                            show={this.isUserOutOfOffice()}
                            onClick={setOnline}
                            ariaLabel={this.props.intl.formatMessage(statusDropdownMessages.ooo.name)}
                            text={this.props.intl.formatMessage(statusDropdownMessages.ooo.name)}
                            icon={(
                                <CancelIcon
                                    color={'rgba(var(--center-channel-color-rgb), 0.56)'}
                                />
                            )}
                            extraText={this.props.intl.formatMessage(statusDropdownMessages.ooo.extra)}
                            rightDecorator={selectedIndicator}
                        />
                    </Menu.Group>
                    {customStatusComponent}
                    <Menu.Group>
                        <Menu.ItemAction
                            onClick={isCurrentUserAdmin ? setOnline : () => {}}
                            ariaLabel={this.props.intl.formatMessage(statusDropdownMessages.online.name)}
                            text={this.props.intl.formatMessage(statusDropdownMessages.online.name)}
                            icon={(
                                <StatusIcon
                                    status={'online'}
                                    className={'status-icon'}
                                />
                            )}
                            rightDecorator={status === 'online' && selectedIndicator}
                            id={'status-menu-online'}
                        />
                        <Menu.ItemAction
                            onClick={isCurrentUserAdmin ? setAway : () => {}}
                            ariaLabel={this.props.intl.formatMessage(statusDropdownMessages.away.name)}
                            text={this.props.intl.formatMessage(statusDropdownMessages.away.name)}
                            icon={(
                                <StatusIcon
                                    status={'away'}
                                    className={'status-icon'}
                                />
                            )}
                            rightDecorator={status === 'away' && selectedIndicator}
                            id={'status-menu-away'}
                        />
                        <Menu.ItemSubMenu
                            subMenu={isCurrentUserAdmin ? dndSubMenuItems : []}
                            ariaLabel={`${this.props.intl.formatMessage(statusDropdownMessages.dnd.name)}. ${dndExtraText}`}
                            text={this.props.intl.formatMessage(statusDropdownMessages.dnd.name)}
                            extraText={dndExtraText}
                            icon={(
                                <StatusIcon
                                    status={'dnd'}
                                    className={'status-icon'}
                                />
                            )}
                            rightDecorator={status === 'dnd' && selectedIndicator}
                            direction={'left'}
                            openUp={this.state.openUp}
                            id={'status-menu-dnd'}
                            action={isCurrentUserAdmin ? () => setDnd(0) : () => {}}
                        />
                        {/* <Menu.ItemAction
                            onClick={setOffline}
                            ariaLabel={this.props.intl.formatMessage(statusDropdownMessages.offline.name)}
                            text={this.props.intl.formatMessage(statusDropdownMessages.offline.name)}
                            icon={(
                                <StatusIcon
                                    status={'offline'}
                                    className={'status-icon'}
                                />
                            )}
                            rightDecorator={status === 'offline' && selectedIndicator}
                            id={'status-menu-offline'}
                        /> */}
                    </Menu.Group>
                    <Menu.Group>
                        <Menu.ItemToggleModalRedux
                            id='accountSettings'
                            ariaLabel='Profile'
                            modalId={ModalIdentifiers.USER_SETTINGS}
                            dialogType={UserSettingsModal}
                            dialogProps={{isContentProductSettings: false}}
                            text={this.props.intl.formatMessage({id: 'navbar_dropdown.profileSettings', defaultMessage: 'Profile'})}
                            icon={
                                <AccountOutlineIcon
                                    size={16}
                                    color={'rgba(var(--center-channel-color-rgb), 0.56)'}
                                />
                            }
                        >
                            {this.props.showCompleteYourProfileTour && (
                                // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
                                <div
                                    onClick={this.handleCompleteYourProfileTask}
                                    className={'account-settings-complete'}
                                >
                                    <CompleteYourProfileTour/>
                                </div>
                            )}
                        </Menu.ItemToggleModalRedux>
                    </Menu.Group>
                    <Menu.Group>
                        <span className={'logout__icon'}>
                            <Menu.ItemAction
                                id='logout'
                                onClick={this.Downloadwindows}
                                text={this.props.intl.formatMessage({id: 'navbar_Download.windows', defaultMessage: 'Download windows'})}
                                icon={
                                    <svg
                                        className='logout__icon_svg'
                                        width='20px'
                                        height='20px'
                                        viewBox='0 0 55 55'
                                        fill='none'
                                        xmlns='http://www.w3.org/2000/svg'
                                    >
                                        <path
                                            d='M24.0625 25.7812V5.58594L0 8.59375V25.7812H24.0625Z'
                                            fill='#00BCD4'
                                        />
                                        <path
                                            d='M27.5 25.7812H55V1.71875L27.5 5.15625V25.7812Z'
                                            fill='#00BCD4'
                                        />
                                        <path
                                            d='M27.5 29.2188V49.8438L55 53.2812V29.2188H27.5Z'
                                            fill='#00BCD4'
                                        />
                                        <path
                                            d='M24.0625 29.2188H0V46.4062L24.0625 49.4141V29.2188Z'
                                            fill='#00BCD4'
                                        />
                                    </svg>

                                }
                            />
                            <Menu.ItemAction
                                id='logout'
                                onClick={this.Downloadmobile}
                                text={this.props.intl.formatMessage({id: 'navbar_Download.mobile', defaultMessage: 'Download mobile'})}
                                icon={
                                    <svg
                                        className='logout__icon_svg_mobeil'
                                        width='60'
                                        height='60'
                                        viewBox='0 0 55 55'
                                        fill='none'
                                        xmlns='http://www.w3.org/2000/svg'
                                    >
                                        <path
                                            d='M36.0938 1.71875H18.9062C15.5839 1.71875 12.8906 4.41204 12.8906 7.73438V47.2656C12.8906 50.588 15.5839 53.2812 18.9062 53.2812H36.0938C39.4161 53.2812 42.1094 50.588 42.1094 47.2656V7.73438C42.1094 4.41204 39.4161 1.71875 36.0938 1.71875Z'
                                            fill='#00987E'
                                        />
                                        <path
                                            d='M16.7578 37.8125C28.386 37.8125 37.8125 26.8469 37.8125 13.3203C37.8278 9.31685 36.9715 5.35803 35.3031 1.71875H18.9062C17.3108 1.71875 15.7807 2.35254 14.6526 3.48069C13.5244 4.60883 12.8906 6.13893 12.8906 7.73438V37.3931C14.1611 37.6699 15.4575 37.8105 16.7578 37.8125Z'
                                            fill='#00987E'
                                        />
                                        <path
                                            d='M27.5 49.8438C28.4492 49.8438 29.2188 49.0742 29.2188 48.125C29.2188 47.1758 28.4492 46.4062 27.5 46.4062C26.5508 46.4062 25.7812 47.1758 25.7812 48.125C25.7812 49.0742 26.5508 49.8438 27.5 49.8438Z'
                                            fill='white'
                                        />
                                        <path
                                            d='M12.8906 8.59375H42.1094V42.9688H12.8906V8.59375Z'
                                            fill='white'
                                        />
                                        <path
                                            d='M12.8906 37.3931C14.1611 37.6699 15.4575 37.8105 16.7578 37.8125C28.386 37.8125 37.8125 26.8469 37.8125 13.3203C37.8122 11.7365 37.68 10.1556 37.4172 8.59375H12.8906V37.3931Z'
                                            fill='white'
                                        />
                                        <path
                                            d='M18.9062 26.8906V35.4844H36.0938V26.8906H32.6562L30.9375 28.6094H24.0625L22.3438 26.8906H18.9062Z'
                                            fill='#F7D881'
                                        />
                                        <path
                                            d='M30.6856 22.2674C30.5245 22.1063 30.3059 22.0158 30.0781 22.0158C29.8502 22.0158 29.6316 22.1063 29.4705 22.2674L28.3593 23.3786V16.8594C28.3593 16.6315 28.2688 16.4129 28.1076 16.2517C27.9464 16.0905 27.7279 16 27.4999 16C27.272 16 27.0534 16.0905 26.8923 16.2517C26.7311 16.4129 26.6406 16.6315 26.6406 16.8594V23.3786L25.5294 22.2674C25.3673 22.1109 25.1502 22.0243 24.9249 22.0262C24.6996 22.0282 24.484 22.1186 24.3247 22.2779C24.1654 22.4372 24.075 22.6528 24.073 22.8781C24.0711 23.1034 24.1577 23.3205 24.3142 23.4826L26.8924 26.0607C26.9724 26.1403 27.0673 26.2034 27.1717 26.2463C27.2756 26.29 27.3872 26.3125 27.4999 26.3125C27.6127 26.3125 27.7243 26.29 27.8282 26.2463C27.9326 26.2034 28.0275 26.1403 28.1075 26.0607L30.6856 23.4826C30.8468 23.3214 30.9373 23.1029 30.9373 22.875C30.9373 22.6471 30.8468 22.4286 30.6856 22.2674Z'
                                            fill='#00987E'
                                        />
                                        <path
                                            d='M32.6562 26.8906L30.9375 28.6094H24.0625L22.3438 26.8906H18.9062V35.4844H24.5042C28.4236 33.6142 31.7003 30.6235 33.9195 26.8906H32.6562Z'
                                            fill='#F9E09A'
                                        />
                                    </svg>

                                }
                            />
                            <Menu.ItemAction
                                id='logout'
                                onClick={this.Downloadtablet}
                                text={this.props.intl.formatMessage({id: 'navbar_Download.tablet', defaultMessage: 'Download tablet'})}
                                icon={

                                    <svg
                                        className='logout__icon_svg_mobeil'
                                        xmlns='http://www.w3.org/2000/svg'
                                        version='1.1'
                                        xmlnsXlink='http://www.w3.org/1999/xlink'
                                        width='60'
                                        height='60'
                                        x='0'
                                        y='0'
                                        viewBox='0 0 33.994 33.994'
                                        xmlSpace='preserve'
                                    >
                                        <g>
                                            <path
                                                d='M27.125 0H6.867a2.304 2.304 0 0 0-2.305 2.309v29.377a2.306 2.306 0 0 0 2.305 2.308h20.258a2.308 2.308 0 0 0 2.306-2.308V2.309A2.305 2.305 0 0 0 27.125 0zM16.997 33.129a1.37 1.37 0 1 1 0-2.742 1.37 1.37 0 0 1 0 2.742zm10.101-3.943H6.896V2.774h20.202v26.412z'
                                                fill='#00987e'
                                                opacity='1'
                                                data-original='#000000'
                                                className=''
                                            />
                                        </g>
                                    </svg>

                                }
                            />
                            <li className='MenuGroup mobile-menu-divider'>
                                <hr/>
                            </li>
                            <Menu.ItemAction
                                id='logout'
                                onClick={this.handleEmitUserLoggedOutEvent}
                                text={this.props.intl.formatMessage({id: 'navbar_dropdown.logout', defaultMessage: 'Log Out'})}
                                icon={
                                    <ExitToAppIcon
                                        size={16}
                                        color={'rgba(var(--center-channel-color-rgb), 0.56)'}
                                    />
                                }
                            />
                        </span>
                    </Menu.Group>
                </Menu>
            </MenuWrapper>
        );
    };
}
export default injectIntl(StatusDropdown);
