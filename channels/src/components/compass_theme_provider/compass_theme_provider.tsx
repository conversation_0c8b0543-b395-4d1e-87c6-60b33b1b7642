// Copyright (c) 2015-present Mattermost, Inc. All Rights Reserved.
// See LICENSE.txt for license information.

import React, {useMemo} from 'react';

import ThemeProvider, {lightTheme} from '@mattermost/compass-components/utilities/theme'; // eslint-disable-line no-restricted-imports

import {Preferences} from 'mattermost-redux/constants';
import type {Theme} from 'mattermost-redux/selectors/entities/preferences';

type Props = {
    theme: Theme;
    children?: React.ReactNode;
}

const CompassThemeProvider = ({
    theme,
    children,
}: Props) => {
    const compassTheme = useMemo(() => {
        // Safety check: if theme is undefined or null, use default theme
        const safeTheme = theme || Preferences.THEMES.denim;

        const base = {
            ...lightTheme,
            noStyleReset: true,
            noDefaultStyle: true,
            noFontFaces: true,
        };

        return {
            ...base,
            palette: {
                ...base.palette,
                primary: {
                    ...base.palette.primary,
                    main: safeTheme.sidebarHeaderBg,
                    contrast: safeTheme.sidebarHeaderTextColor,
                },
                alert: {
                    ...base.palette.alert,
                    main: safeTheme.dndIndicator,
                },
            },
            action: {
                ...base.action,
                hover: safeTheme.sidebarHeaderTextColor,
                disabled: safeTheme.sidebarHeaderTextColor,
            },
            badges: {
                ...base.badges,
                online: safeTheme.onlineIndicator,
                away: safeTheme.awayIndicator,
                dnd: safeTheme.dndIndicator,
            },
            text: {
                ...base.text,
                primary: safeTheme.sidebarHeaderTextColor,
            },
        };
    }, [theme]);

    return (
        <ThemeProvider theme={compassTheme}>
            {children}
        </ThemeProvider>
    );
};

export default CompassThemeProvider;
