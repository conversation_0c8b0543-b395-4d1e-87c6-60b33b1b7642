$primary-color: rgb(0, 152, 126);
$bg-light: rgba(241, 241, 241, 1);
$font-base: 17px;
$font-small: 12px;
$spacing-xs: 5px;
$spacing-sm: 8px;
$radius-sm: 9px;
$radius-lg: 12px;
$max-msg-width: 60%;

.buzz-message svg {
  width: 90px;
  height: 90px;

}

.post--right1,
.post--left1:has(.file__image) {
  border-radius: 1px 9px 9px 9px;
  width: fit-content;
  min-width: 70px;
  max-width: 60% !important;
  text-align: start;
  position: relative;
  align-items: start !important;
  flex-direction: column;
  overflow: auto;

  .image-loaded-container {
    overflow: hidden;
    width: 100%;
    position: relative;
    bottom: 5px;
    padding: 2px 2px 2px 2px;
    border-radius: 12px 12px 0 0;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.attachment--pretext,
.attachment__container {
  color: var(--sidebar-text) !important;
}

.post--left {
  direction: rtl;
  text-align: start;

  .mention-link {
    color: #f5f5f5
  }

  .mention-link:hover {
    color: #000;
  }

  .col {
    &:lang(ar) {
      right: auto !important;
      left: 0 !important;
      top: 0 !important;
    }

    &:lang(en) {
      right: 0 !important;
      left: auto !important;
    }

    .Menu__content.dropdown-menu.dropdown-menus.openLeft.openUp {
      min-width: fit-content;

      &:lang(ar) {
        left: 0 !important;
        right: auto !important;
      }

      &:lang(en) {
        left: auto !important;
        right: 0 !important;
      }
    }
  }
}

.post--right {
  direction: ltr;
  text-align: start;

  .post-edited__indicator {
    color: #000 !important;
  }

  .cardButton {
    display: none;
  }

  .col {
    &:dir(ltr) {
      left: 0 !important;
      right: auto !important;
    }

    &:dir(rtl) {
      left: auto !important;
      right: 0 !important;
    }

    .Menu__content.dropdown-menu.dropdown-menus.openLeft.openUp {
      min-width: fit-content;

      &:dir(ltr) {
        left: 0 !important;
        right: auto !important;
      }

      &:dir(rtl) {
        left: auto !important;
        right: 0 !important;
      }
    }
  }

}

.post--left1,
.post--right1 {
  padding: 8px;
  font-size: $font-base;
  position: relative;

  .post__time {
    font-weight: 500;
  }

  .post-image__column {
    overflow: visible;
  }
}

.post--left1 {
  padding-bottom: 0;

  .theme span {
    color: white;
  }

  .post__time {
    line-height: 2px !important;
    color: white;
  }

  display: flex;
  flex-direction: column;
  border-radius: 13px;
  background-color: $primary-color;
  width: fit-content;
  min-width: 70px;
  max-width: 900px;
  color: white;
  text-align: start;

  .group-mention-link,
  .markdown__link span {
    color: #f0f0f0;
  }

  .post-edited__indicator {
    color: #f0f0f0 !important;
  }

  ul:dir(rtl),
  ol:dir(rtl) {
    &:dir(rtl) {
      padding-right: 22px;
    }

    &:dir(ltr) {
      padding-left: 22px;
    }
  }

  .cardButton button {
    border: none;
  }

  table {
    color: var(--sidebar-text);
  }

  td a span {
    color: rgb(0, 152, 126) !important;
  }

  &.first-post {
    border-radius: 13px 0 13px 13px;

    &::after {
      content: "";
      position: absolute;
      width: 30px;
      height: 30px;
      top: 0;
      right: -7px;
      background: var(--sofa-color);
      clip-path: polygon(100% -13%, 72% 0, 72% 25%);
    }
  }

  &:has(.markdown__table) {
    overflow-x: auto;
  }
}

.post--right1 {
  border-radius: 13px;
  background-color: $bg-light;
  margin-top: 4px;
  color: black;
  width: fit-content;
  min-width: 70px;
  max-width: 900px;
  text-align: left;

  ul:dir(rtl),
  ol:dir(rtl) {
    &:dir(rtl) {
      padding-right: 22px;
    }

    &:dir(ltr) {
      padding-left: 22px;
    }
  }

  .mention-link,
  .mention-link:hover,
  .mention-link:focus {
    color: var(--sofa-color) !important;
  }

  .post__time {
    color: #000;
  }

  &.first-post {
    border-radius: 0px 13px 13px 13px;

    &::after {
      content: "";
      position: absolute;
      width: 30px;
      height: 30px;
      top: 0;
      background: inherit;
      left: -21px;
      right: auto;
      clip-path: polygon(50% -13%, 72% 0, 72% 25%);
    }
  }

  .preview {
    .preview__header {
      flex-direction: row-reverse;
    }

    .preview__content__text {
      order: 2;
      justify-content: end;
    }
  }
}