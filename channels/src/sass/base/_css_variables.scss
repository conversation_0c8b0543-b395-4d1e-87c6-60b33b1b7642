:root {
    --icon-opacity: 0.64;
    --icon-opacity-hover: 0.8;
    --away-indicator: #ffbc42;
    --away-indicator-dark: #c79e3f;
    --button-bg: #00987E;
    --button-color: #fff;
    --center-channel-bg: #fff;
    --center-channel-color: #292c33;
    --dnd-indicator: #d24b4e;
    --error-text: #d24b4e;
    --warning-text: #cc8f00;
    --link-color: #00987E;
    --mention-bg: #fff;
    --mention-color:  #00987E;
    --mention-highlight-bg: #ffe577;
    --mention-highlight-link:  #00987E;
    --new-message-separator: #f80;
    --online-indicator: #06d6a0;
    --sidebar-bg:  #00987E;
    --sidebar-header-bg:  #00987E;
    --sidebar-header-text-color: #fff;
    --sidebar-text: #fff;
    --sidebar-text-active-border:  #00987E;
    --sidebar-text-active-color: #fff;
    --sidebar-text-hover-bg: #00987E;
    --sidebar-unread-text: #fff;
    --sidebar-team-background:  #00987E;
    --secondary-blue:  #00987E;
    --denim-button-bg: #00987E;
    --denim-status-online: #3db887;
    --denim-sidebar-active-border: #00987E;
    --title-color-indigo-500: #1e325c;
    --sofa-color: #00987E;
    --away-indicator-rgb: 255, 188, 66;
    --button-bg-rgb: 22, 109, 224;
    --button-color-rgb: 255, 255, 255;
    --center-channel-bg-rgb: 255, 255, 255;
    --center-channel-color-rgb: 61, 60, 64;
    --dnd-indicator-rgb: 247, 67, 67;
    --error-text-color-rgb: 253, 89, 96;
    --link-color-rgb: 35, 137, 215;
    --mention-bg-rgb: 255, 255, 255;
    --mention-color-rgb: 20, 93, 191;
    --mention-highlight-bg-rgb: 255, 229, 119;
    --mention-highlight-link-rgb: 22, 109, 224;
    --new-message-separator-rgb: 255, 136, 0;
    --online-indicator-rgb: 6, 214, 160;
    --sidebar-bg-rgb: 20, 93, 191;
    --sidebar-header-bg-rgb: 17, 83, 171;
    --sidebar-header-text-color-rgb: 255, 255, 255;
    --sidebar-text-rgb: 255, 255, 255;
    --sidebar-text-active-border-rgb: 87, 158, 255;
    --sidebar-text-active-color-rgb: 255, 255, 255;
    --sidebar-text-hover-bg-rgb: 69, 120, 191;
    --sidebar-unread-text-rgb: 255, 255, 255;
    --error-box-background: 197, 67, 72;
    --sidebar-team-background-rgb: 11, 66, 140;
    --secondary-blue-rgb: 34, 64, 109;
    --denim-status-online-rgb: 61, 184, 135;
    --denim-button-bg-rgb: 28, 88, 217;
    --title-color-indigo-500-rgb: 30, 50, 92;

    // offline indicator color stays the same in all themes, that's why it is separated from the other variables
    // the color specified here is the new hard-coded color from the compass design system
    --offline-indicator: rgba(175, 179, 192, 0.75);

    // Elevation values used for box shadows.
    // Defined as CSS variables so that both sass and JS can use them.
    --elevation-1: 0 2px 3px 0 rgba(0, 0, 0, 0.08);
    --elevation-2: 0 4px 6px 0 rgba(0, 0, 0, 0.12);
    --elevation-3: 0 6px 14px 0 rgba(0, 0, 0, 0.12);
    --elevation-4: 0 8px 24px 0 rgba(0, 0, 0, 0.12);
    --elevation-5: 0 12px 32px 0 rgba(0, 0, 0, 0.12);
    --elevation-6: 0 20px 32px 0 rgba(0, 0, 0, 0.12);

    // Corner Radius variables
    --radius-xs: 2px;
    --radius-s: 4px;
    --radius-m: 8px;
    --radius-l: 12px;
    --radius-xl: 16px;
    --radius-full: 50%;

    // Border variables
    --border-default: solid 1px rgba(var(--center-channel-color-rgb), 0.12);
    --border-light: solid 1px rgba(var(--center-channel-color-rgb), 0.08);
    --border-dark: solid 1px rgba(var(--center-channel-color-rgb), 0.16);

    // Global Header variables
    --global-header-background: var(--sidebar-teambar-bg);
    --global-header-text: var(--sidebar-header-text-color);
    --global-header-background-rgb: var(--sidebar-teambar-bg-rgb);
    --global-header-text-rgb: var(--sidebar-header-text-color-rgb);

    // semantic colors
    // not to be overwritten, since they are shared among themes
    --semantic-color-general: var(--center-channel-color-rgb);
    --semantic-color-info: 93, 137, 234;
    --semantic-color-success: 61, 184, 135;
    --semantic-color-warning: 245, 171, 0;
    --semantic-color-danger: 210, 75, 78;

    // Do not use these variables
    // use --center-channel-color instead
    --center-channel-text: var(--center-channel-color);
    // use --center-channel-color-rgb instead
    --center-channel-text-rgb: var(--center-channel-color-rgb);
}
